# MIDI音乐统计分析项目

本项目对两个MIDI文件（棕色音乐和粉色音乐）进行了全面的统计分析，包括音高分布、音名分布、时值分布、音程分布和音名转移概率矩阵等。

## 文件说明

### 输入文件
- `brown.mid` - 棕色音乐MIDI文件
- `pink.mid` - 粉色音乐MIDI文件

### 分析脚本
- `midi_analysis.py` - 主要的MIDI分析类和可视化函数（包含功率谱密度分析）
- `analysis_report.py` - 生成综合分析报告
- `export_data.py` - 导出分析数据到CSV和JSON格式
- `power_spectrum_report.py` - 功率谱密度分析专题报告
- `requirements.txt` - Python依赖包列表

### 可视化图表（PNG格式）
- `pitch_distribution.png` - 音高分布对比图
- `note_name_distribution.png` - 音名分布对比图
- `duration_distribution.png` - 时值分布对比图
- `interval_distribution.png` - 音程分布对比图
- `transition_matrix.png` - 音名转移概率矩阵热力图
- `power_spectral_density.png` - 功率谱密度和自相关函数对比图
- `pitch_sequence_analysis.png` - 音高序列和音高变化分析图

### 数据文件（CSV格式）
- `pitch_distribution.csv` - 音高分布数据
- `note_name_distribution.csv` - 音名分布数据
- `duration_distribution.csv` - 时值分布数据
- `interval_distribution.csv` - 音程分布数据
- `brown_transition_matrix.csv` - 棕色音乐转移矩阵
- `pink_transition_matrix.csv` - 粉色音乐转移矩阵
- `brown_notes.csv` - 棕色音乐原始音符数据
- `pink_notes.csv` - 粉色音乐原始音符数据

### 汇总文件
- `analysis_summary.json` - 分析结果汇总（JSON格式）

## 分析结果摘要

### 基本信息
- 棕色音乐：100个音符
- 粉色音乐：100个音符

### 主要发现
1. **音高范围**：棕色音乐更宽广（29 vs 25半音）
2. **音名分布**：棕色音乐的音名分布更均匀
3. **时值多样性**：两种音乐的时值种类相同（5种）
4. **音程跳跃**：粉色音乐的音程跳跃更大（-10到13半音 vs -3到3半音）
5. **转移随机性**：粉色音乐的音名转移更随机（熵值更高）

### 统计指标
- **音高分布熵**：棕色音乐 4.540 bits，粉色音乐 4.225 bits
- **音名分布熵**：棕色音乐 3.524 bits，粉色音乐 3.512 bits
- **时值分布熵**：棕色音乐 2.269 bits，粉色音乐 1.850 bits
- **转移熵**：棕色音乐 25.329 bits，粉色音乐 28.340 bits

### 功率谱密度分析结果
- **主导频率**：棕色音乐 0.0196（周期51音符），粉色音乐 0.0392（周期25.5音符）
- **最大功率谱密度**：棕色音乐 16.29，粉色音乐 5.97
- **功率分布**：
  - 棕色音乐：低频62.4%，中频24.4%，高频13.3%
  - 粉色音乐：低频40.9%，中频35.6%，高频23.5%
- **音高变化方差**：棕色音乐 3.98，粉色音乐 27.32

## 使用方法

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行分析
```bash
# 生成可视化图表
python midi_analysis.py

# 生成分析报告
python analysis_report.py

# 导出数据文件
python export_data.py

# 生成功率谱密度分析报告
python power_spectrum_report.py
```

## 技术说明

### 分析方法
1. **音高分布**：统计每个MIDI音符号（0-127）的出现频率
2. **音名分布**：将音高映射到12个半音，统计相对频率
3. **时值分布**：统计音符持续时间的分布
4. **音程分布**：计算相邻音符之间的半音距离
5. **转移矩阵**：构建12x12的音名转移概率矩阵
6. **功率谱密度**：将音乐视为音高序列，计算自相关函数的傅里叶变换

### 数据格式
- **CSV文件**：便于在Excel或其他数据分析工具中使用
- **JSON文件**：便于程序化处理和进一步分析
- **PNG图表**：直观的可视化结果

### 熵值解释
熵值用于衡量分布的随机性和均匀程度：
- 熵值越高表示分布越均匀、越随机
- 熵值越低表示分布越集中、越有规律

## 音频频谱分析 (新增功能)

### 音频分析脚本
- `audio_spectrum_analysis.py` - 音频频谱分析主脚本
- `audio_analysis_report.py` - 音频分析综合报告

### 音频分析功能
1. **时域分析**：波形对比，振幅变化分析
2. **频域分析**：频谱对比，主要频率成分识别
3. **频谱图分析**：时频分析，频谱重心变化
4. **音色特征分析**：
   - 频谱重心（音色明亮度）
   - 频谱带宽（音色丰富度）
   - 频谱滚降点（90%能量频率）
   - 频谱平坦度（谐波性）
   - 频率能量分布（低频、中频、高频）

### 音频分析结果
- `audio_waveform_comparison.png` - 时域波形对比图
- `audio_spectrum_comparison.png` - 频域频谱对比图
- `audio_spectrogram_comparison.png` - 频谱图和频谱重心变化图

### 主要发现
**棕色音乐 vs 粉色音乐（钢琴音色）：**
- **音色明亮度**：粉色音乐更明亮（频谱重心1033.9 vs 1011.7 Hz）
- **音色丰富度**：粉色音乐更丰富（频谱带宽1112.0 vs 1074.4 Hz）
- **谐波特性**：棕色音乐更具谐波性（频谱平坦度0.1318 vs 0.1365）
- **能量分布**：棕色音乐中频能量更集中（50.5% vs 43.7%）
- **主要频率**：棕色音乐以C#5、D5为主，粉色音乐以F#4、E4为主

### 运行音频分析
```bash
# 运行音频频谱分析
python audio_spectrum_analysis.py

# 生成音频分析综合报告
python audio_analysis_report.py
```

## 扩展分析建议

1. **和声分析**：分析同时发声的音符组合
2. **节奏模式**：分析时值序列的模式
3. **调性分析**：识别音乐的调性特征
4. **复杂度分析**：计算音乐的复杂度指标
5. **相似性分析**：比较两段音乐的相似程度
6. **谐波分析**：分析频谱中的谐波成分
7. **音色特征**：提取更多音色描述符（如谱质心、谱滚降等）
