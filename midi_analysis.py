import mido
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from collections import defaultdict, Counter
from scipy import signal
from scipy.fft import fft, fftfreq
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MIDIAnalyzer:
    def __init__(self, midi_file):
        self.midi_file = midi_file
        self.notes = []  # 存储音符信息 (pitch, duration, start_time)
        self.note_names = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
        self.ticks_per_beat = 480  # 默认值，会从MIDI文件中读取

    def midi_to_note_name(self, midi_note):
        """将MIDI音符号转换为音名（如C4）"""
        octave = (midi_note // 12) - 1
        note_name = self.note_names[midi_note % 12]
        return f"{note_name}{octave}"

    def ticks_to_note_value(self, ticks):
        """将ticks转换为音符时值名称"""
        # 基于标准的ticks_per_beat计算
        beat_ratio = ticks / self.ticks_per_beat

        if beat_ratio >= 4:
            return "全音符"
        elif beat_ratio >= 2:
            return "二分音符"
        elif beat_ratio >= 1:
            return "四分音符"
        elif beat_ratio >= 0.5:
            return "八分音符"
        elif beat_ratio >= 0.25:
            return "十六分音符"
        else:
            return None  # 不统计更短的音符

    def parse_midi(self):
        """解析MIDI文件，提取音符信息"""
        mid = mido.MidiFile(self.midi_file)
        self.ticks_per_beat = mid.ticks_per_beat

        # 存储当前播放的音符 (pitch -> start_time)
        active_notes = {}
        current_time = 0

        for track in mid.tracks:
            current_time = 0
            for msg in track:
                current_time += msg.time

                if msg.type == 'note_on' and msg.velocity > 0:
                    # 音符开始
                    active_notes[msg.note] = current_time
                elif msg.type == 'note_off' or (msg.type == 'note_on' and msg.velocity == 0):
                    # 音符结束
                    if msg.note in active_notes:
                        start_time = active_notes[msg.note]
                        duration = current_time - start_time
                        self.notes.append((msg.note, duration, start_time))
                        del active_notes[msg.note]

        # 按开始时间排序
        self.notes.sort(key=lambda x: x[2])
        print(f"解析完成：{self.midi_file}，共找到 {len(self.notes)} 个音符")

    def get_pitch_distribution(self):
        """获取音高分布"""
        pitches = [note[0] for note in self.notes]
        pitch_counts = Counter(pitches)
        total_notes = len(pitches)

        # 转换为概率
        pitch_probs = {pitch: count/total_notes for pitch, count in pitch_counts.items()}
        return pitch_probs

    def get_note_name_distribution(self):
        """获取音名分布（忽略八度）"""
        note_names = [self.note_names[note[0] % 12] for note in self.notes]
        name_counts = Counter(note_names)
        total_notes = len(note_names)

        # 转换为概率
        name_probs = {name: count/total_notes for name, count in name_counts.items()}
        return name_probs

    def get_duration_distribution(self):
        """获取时值分布"""
        durations = [note[1] for note in self.notes]
        duration_counts = Counter(durations)
        total_notes = len(durations)

        # 转换为概率
        duration_probs = {dur: count/total_notes for dur, count in duration_counts.items()}
        return duration_probs

    def get_duration_by_note_value(self):
        """按音符时值分类获取分布"""
        duration_by_value = {}
        valid_notes = 0  # 统计有效音符数量

        for note in self.notes:
            duration = note[1]
            note_value = self.ticks_to_note_value(duration)
            if note_value is not None:  # 只统计有效的音符时值
                if note_value not in duration_by_value:
                    duration_by_value[note_value] = 0
                duration_by_value[note_value] += 1
                valid_notes += 1

        # 基于有效音符数量计算概率
        return {value: count/valid_notes for value, count in duration_by_value.items()} if valid_notes > 0 else {}

    def get_interval_distribution(self):
        """获取前后音高相隔半音数分布"""
        if len(self.notes) < 2:
            return {}

        intervals = []
        for i in range(1, len(self.notes)):
            interval = self.notes[i][0] - self.notes[i-1][0]
            intervals.append(interval)

        interval_counts = Counter(intervals)
        total_intervals = len(intervals)

        # 转换为概率
        interval_probs = {interval: count/total_intervals for interval, count in interval_counts.items()}
        return interval_probs

    def get_note_transition_matrix(self):
        """获取音名转移概率矩阵"""
        if len(self.notes) < 2:
            return np.zeros((12, 12))

        # 创建转移计数矩阵
        transition_counts = np.zeros((12, 12))

        for i in range(1, len(self.notes)):
            from_note = self.notes[i-1][0] % 12
            to_note = self.notes[i][0] % 12
            transition_counts[from_note][to_note] += 1

        # 转换为概率矩阵
        transition_probs = np.zeros((12, 12))
        for i in range(12):
            row_sum = np.sum(transition_counts[i])
            if row_sum > 0:
                transition_probs[i] = transition_counts[i] / row_sum

        return transition_probs

    def get_pitch_sequence(self):
        """获取音高序列（按时间顺序）"""
        # 按开始时间排序的音符序列
        sorted_notes = sorted(self.notes, key=lambda x: x[2])  # 按开始时间排序
        pitch_sequence = [note[0] for note in sorted_notes]  # 提取音高
        return np.array(pitch_sequence)

    def compute_autocorrelation(self, sequence, max_lag=None):
        """计算序列的自相关函数"""
        n = len(sequence)
        if max_lag is None:
            max_lag = n - 1

        # 去除均值
        sequence_centered = sequence

        # 计算自相关函数
        autocorr = np.correlate(sequence_centered, sequence_centered, mode='full')

        # 取正延迟部分
        autocorr = autocorr[n-1:]

        # 除以延迟长度
        for i in range(len(autocorr)):
            autocorr[i] = autocorr[i] / (n - i)

        # 归一化
        # autocorr = autocorr / autocorr[0]
        autocorr = autocorr - np.mean(sequence_centered) ** 2

        # 限制最大延迟
        autocorr = autocorr[:max_lag+1]

        return autocorr

    def compute_power_spectral_density(self, max_lag=None):
        """计算音高序列的功率谱密度"""
        # 获取音高序列
        pitch_sequence = self.get_pitch_sequence()

        if len(pitch_sequence) < 2:
            return None, None

        # 设置最大延迟
        if max_lag is None:
            max_lag = min(len(pitch_sequence) - 1, 50)  # 限制最大延迟为50或序列长度-1

        # 计算自相关函数
        autocorr = self.compute_autocorrelation(pitch_sequence, max_lag)

        # 对自相关函数进行傅里叶变换得到功率谱密度
        psd = np.abs(fft(autocorr))

        # 计算对应的频率
        freqs = fftfreq(len(autocorr), d=1.0)  # 归一化频率

        # 只取正频率部分
        positive_freqs = freqs[:len(freqs)//2]
        positive_psd = psd[:len(psd)//2]

        return positive_freqs, positive_psd

    def analyze_pitch_sequence_statistics(self):
        """分析音高序列的统计特性"""
        pitch_sequence = self.get_pitch_sequence()

        if len(pitch_sequence) < 2:
            return {}

        stats = {
            'sequence_length': len(pitch_sequence),
            'mean_pitch': np.mean(pitch_sequence),
            'std_pitch': np.std(pitch_sequence),
            'min_pitch': np.min(pitch_sequence),
            'max_pitch': np.max(pitch_sequence),
            'pitch_range': np.max(pitch_sequence) - np.min(pitch_sequence)
        }

        # 计算音高变化的统计特性
        pitch_diff = np.diff(pitch_sequence)
        stats.update({
            'mean_pitch_change': np.mean(pitch_diff),
            'std_pitch_change': np.std(pitch_diff),
            'max_pitch_jump': np.max(np.abs(pitch_diff)),
            'pitch_change_variance': np.var(pitch_diff)
        })

        return stats

def plot_pitch_distribution(analyzer1, analyzer2, title1, title2):
    """绘制音高分布对比图（使用音名标识）"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    # 棕色音乐
    pitch_probs1 = analyzer1.get_pitch_distribution()
    pitches1 = sorted(pitch_probs1.keys())
    probs1 = [pitch_probs1[p] for p in pitches1]
    note_labels1 = [analyzer1.midi_to_note_name(p) for p in pitches1]

    bars1 = ax1.bar(range(len(pitches1)), probs1, color='brown', alpha=0.7)
    ax1.set_title(f'{title1} - 音高分布')
    ax1.set_xlabel('音名')
    ax1.set_ylabel('出现概率')
    ax1.set_xticks(range(len(pitches1)))
    ax1.set_xticklabels(note_labels1, rotation=45)
    ax1.grid(True, alpha=0.3)

    # 粉色音乐
    pitch_probs2 = analyzer2.get_pitch_distribution()
    pitches2 = sorted(pitch_probs2.keys())
    probs2 = [pitch_probs2[p] for p in pitches2]
    note_labels2 = [analyzer2.midi_to_note_name(p) for p in pitches2]

    bars2 = ax2.bar(range(len(pitches2)), probs2, color='pink', alpha=0.7)
    ax2.set_title(f'{title2} - 音高分布')
    ax2.set_xlabel('音名')
    ax2.set_ylabel('出现概率')
    ax2.set_xticks(range(len(pitches2)))
    ax2.set_xticklabels(note_labels2, rotation=45)
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('pitch_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_note_name_distribution(analyzer1, analyzer2, title1, title2):
    """绘制音名分布对比图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

    note_names = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']

    # 棕色音乐
    name_probs1 = analyzer1.get_note_name_distribution()
    probs1 = [name_probs1.get(name, 0) for name in note_names]

    ax1.bar(note_names, probs1, color='brown', alpha=0.7)
    ax1.set_title(f'{title1} - 音名分布')
    ax1.set_xlabel('音名')
    ax1.set_ylabel('出现概率')
    ax1.grid(True, alpha=0.3)

    # 粉色音乐
    name_probs2 = analyzer2.get_note_name_distribution()
    probs2 = [name_probs2.get(name, 0) for name in note_names]

    ax2.bar(note_names, probs2, color='pink', alpha=0.7)
    ax2.set_title(f'{title2} - 音名分布')
    ax2.set_xlabel('音名')
    ax2.set_ylabel('出现概率')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('note_name_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_duration_distribution(analyzer1, analyzer2, title1, title2):
    """绘制时值分布对比图（使用音符名称和直方图）"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

    # 定义音符时值顺序（不包括三十二分音符和更短音符）
    note_value_order = ["十六分音符", "八分音符", "四分音符", "二分音符", "全音符"]

    # 棕色音乐
    duration_probs1 = analyzer1.get_duration_by_note_value()
    values1 = [duration_probs1.get(nv, 0) for nv in note_value_order]

    ax1.bar(note_value_order, values1, color='brown', alpha=0.7)
    ax1.set_title(f'{title1} - 时值分布')
    ax1.set_xlabel('音符时值')
    ax1.set_ylabel('出现概率')
    ax1.tick_params(axis='x', rotation=45)
    ax1.grid(True, alpha=0.3)

    # 粉色音乐
    duration_probs2 = analyzer2.get_duration_by_note_value()
    values2 = [duration_probs2.get(nv, 0) for nv in note_value_order]

    ax2.bar(note_value_order, values2, color='pink', alpha=0.7)
    ax2.set_title(f'{title2} - 时值分布')
    ax2.set_xlabel('音符时值')
    ax2.set_ylabel('出现概率')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('duration_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_interval_distribution(analyzer1, analyzer2, title1, title2):
    """绘制音程分布对比图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 棕色音乐
    interval_probs1 = analyzer1.get_interval_distribution()
    intervals1 = list(interval_probs1.keys())
    probs1 = list(interval_probs1.values())

    ax1.bar(intervals1, probs1, color='brown', alpha=0.7)
    ax1.set_title(f'{title1} - 音程分布')
    ax1.set_xlabel('音程 (半音数)')
    ax1.set_ylabel('出现概率')
    ax1.grid(True, alpha=0.3)

    # 粉色音乐
    interval_probs2 = analyzer2.get_interval_distribution()
    intervals2 = list(interval_probs2.keys())
    probs2 = list(interval_probs2.values())

    ax2.bar(intervals2, probs2, color='pink', alpha=0.7)
    ax2.set_title(f'{title2} - 音程分布')
    ax2.set_xlabel('音程 (半音数)')
    ax2.set_ylabel('出现概率')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('interval_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_transition_matrix(analyzer1, analyzer2, title1, title2):
    """绘制音名转移概率矩阵热力图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    note_names = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']

    # 棕色音乐
    matrix1 = analyzer1.get_note_transition_matrix()
    sns.heatmap(matrix1, annot=True, fmt='.3f', cmap='Reds',
                xticklabels=note_names, yticklabels=note_names, ax=ax1)
    ax1.set_title(f'{title1} - 音名转移概率矩阵')
    ax1.set_xlabel('转移到')
    ax1.set_ylabel('转移自')

    # 粉色音乐
    matrix2 = analyzer2.get_note_transition_matrix()
    sns.heatmap(matrix2, annot=True, fmt='.3f', cmap='RdPu',
                xticklabels=note_names, yticklabels=note_names, ax=ax2)
    ax2.set_title(f'{title2} - 音名转移概率矩阵')
    ax2.set_xlabel('转移到')
    ax2.set_ylabel('转移自')

    plt.tight_layout()
    plt.savefig('transition_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_power_spectral_density(analyzer1, analyzer2, title1, title2):
    """绘制功率谱密度对比图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 计算功率谱密度
    freqs1, psd1 = analyzer1.compute_power_spectral_density(max_lag=64)
    freqs2, psd2 = analyzer2.compute_power_spectral_density(max_lag=64)

    if freqs1 is None or freqs2 is None:
        print("无法计算功率谱密度")
        return

    # 棕色音乐功率谱密度
    ax1.plot(freqs1[1:], psd1[1:], color='brown', alpha=0.7, linewidth=2)
    ax1.plot(freqs1[1:], freqs1[1:] ** -2 / freqs1[1] ** -2 * psd1[1], color='black', linestyle='--', alpha=0.5, linewidth=1)
    ax1.plot(freqs1[1:], freqs1[1:] ** -1 / freqs1[1] ** -1 * psd1[1], color='black', linestyle='--', alpha=0.5, linewidth=1)
    ax1.set_title(f'{title1} - 功率谱密度')
    ax1.set_xlabel('归一化频率')
    ax1.set_ylabel('功率谱密度')
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(0, 0.5)  # 只显示0到奈奎斯特频率

    # 粉色音乐功率谱密度
    ax2.plot(freqs2[1:], psd2[1:], color='pink', alpha=0.7, linewidth=2)
    ax2.plot(freqs2[1:], freqs2[1:] ** -2 / freqs2[1] ** -2 * psd2[1], color='black', linestyle='--', alpha=0.5, linewidth=1)
    ax2.plot(freqs2[1:], freqs2[1:] ** -1 / freqs2[1] ** -1 * psd2[1], color='black', linestyle='--', alpha=0.5, linewidth=1)
    ax2.set_title(f'{title2} - 功率谱密度')
    ax2.set_xlabel('归一化频率')
    ax2.set_ylabel('功率谱密度')
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(0, 0.5)  # 只显示0到奈奎斯特频率

    # 计算并显示自相关函数
    pitch_seq1 = analyzer1.get_pitch_sequence()
    pitch_seq2 = analyzer2.get_pitch_sequence()

    max_lag = min(len(pitch_seq1) - 1, len(pitch_seq2) - 1, 64)  # 限制显示范围

    autocorr1 = analyzer1.compute_autocorrelation(pitch_seq1, max_lag)
    autocorr2 = analyzer2.compute_autocorrelation(pitch_seq2, max_lag)

    lags = np.arange(len(autocorr1))

    # 棕色音乐自相关函数
    ax3.plot(lags, autocorr1, color='brown', alpha=0.7, linewidth=2, marker='o', markersize=4)
    ax3.set_title(f'{title1} - 音高序列自相关函数')
    ax3.set_xlabel('延迟 (音符数)')
    ax3.set_ylabel('自相关系数')
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    # 粉色音乐自相关函数
    lags2 = np.arange(len(autocorr2))
    ax4.plot(lags2, autocorr2, color='pink', alpha=0.7, linewidth=2, marker='o', markersize=4)
    ax4.set_title(f'{title2} - 音高序列自相关函数')
    ax4.set_xlabel('延迟 (音符数)')
    ax4.set_ylabel('自相关系数')
    ax4.grid(True, alpha=0.3)
    ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    plt.tight_layout()
    plt.savefig('power_spectral_density.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_pitch_sequence_analysis(analyzer1, analyzer2, title1, title2):
    """绘制音高序列分析图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 获取音高序列
    pitch_seq1 = analyzer1.get_pitch_sequence()
    pitch_seq2 = analyzer2.get_pitch_sequence()

    # 音高序列时间图
    ax1.plot(range(len(pitch_seq1)), pitch_seq1, color='brown', alpha=0.7, linewidth=1, marker='o', markersize=3)
    ax1.set_title(f'{title1} - 音高序列')
    ax1.set_xlabel('音符序号')
    ax1.set_ylabel('MIDI音高')
    ax1.grid(True, alpha=0.3)

    ax2.plot(range(len(pitch_seq2)), pitch_seq2, color='pink', alpha=0.7, linewidth=1, marker='o', markersize=3)
    ax2.set_title(f'{title2} - 音高序列')
    ax2.set_xlabel('音符序号')
    ax2.set_ylabel('MIDI音高')
    ax2.grid(True, alpha=0.3)

    # 音高变化序列
    pitch_diff1 = np.diff(pitch_seq1)
    pitch_diff2 = np.diff(pitch_seq2)

    ax3.plot(range(len(pitch_diff1)), pitch_diff1, color='brown', alpha=0.7, linewidth=1, marker='o', markersize=3)
    ax3.set_title(f'{title1} - 音高变化序列')
    ax3.set_xlabel('音符序号')
    ax3.set_ylabel('音高变化 (半音)')
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    ax4.plot(range(len(pitch_diff2)), pitch_diff2, color='pink', alpha=0.7, linewidth=1, marker='o', markersize=3)
    ax4.set_title(f'{title2} - 音高变化序列')
    ax4.set_xlabel('音符序号')
    ax4.set_ylabel('音高变化 (半音)')
    ax4.grid(True, alpha=0.3)
    ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    plt.tight_layout()
    plt.savefig('pitch_sequence_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()


def print_statistics(analyzer, title):
    """打印统计信息"""
    print(f"\n=== {title} 统计信息 ===")
    print(f"总音符数: {len(analyzer.notes)}")
    print(f"MIDI时间分辨率: {analyzer.ticks_per_beat} ticks per beat")

    # 音高统计
    pitch_probs = analyzer.get_pitch_distribution()
    min_pitch = min(pitch_probs.keys())
    max_pitch = max(pitch_probs.keys())
    most_common_pitch = max(pitch_probs, key=pitch_probs.get)

    print(f"音高范围: {analyzer.midi_to_note_name(min_pitch)} - {analyzer.midi_to_note_name(max_pitch)}")
    print(f"最常见音高: {analyzer.midi_to_note_name(most_common_pitch)} (概率: {pitch_probs[most_common_pitch]:.3f})")

    # 音名统计
    name_probs = analyzer.get_note_name_distribution()
    print(f"最常见音名: {max(name_probs, key=name_probs.get)} (概率: {max(name_probs.values()):.3f})")

    # 时值统计（改进版）
    duration_by_value = analyzer.get_duration_by_note_value()
    print(f"时值分布:")
    for note_value, prob in sorted(duration_by_value.items(), key=lambda x: x[1], reverse=True):
        print(f"  {note_value}: {prob:.3f}")

    # 音程统计
    interval_probs = analyzer.get_interval_distribution()
    if interval_probs:
        intervals = list(interval_probs.keys())
        print(f"音程范围: {min(intervals)} - {max(intervals)} 半音")
        print(f"最常见音程: {max(interval_probs, key=interval_probs.get)} 半音 (概率: {max(interval_probs.values()):.3f})")

    # 音高序列统计特性
    pitch_stats = analyzer.analyze_pitch_sequence_statistics()
    if pitch_stats:
        print(f"音高序列统计:")
        print(f"  序列长度: {pitch_stats['sequence_length']} 个音符")
        print(f"  平均音高: {pitch_stats['mean_pitch']:.1f} ({analyzer.midi_to_note_name(int(pitch_stats['mean_pitch']))})")
        print(f"  音高标准差: {pitch_stats['std_pitch']:.2f} 半音")
        print(f"  音高范围: {analyzer.midi_to_note_name(pitch_stats['min_pitch'])} - {analyzer.midi_to_note_name(pitch_stats['max_pitch'])}")
        print(f"  平均音高变化: {pitch_stats['mean_pitch_change']:.2f} 半音")
        print(f"  最大音高跳跃: {pitch_stats['max_pitch_jump']:.0f} 半音")
        print(f"  音高变化方差: {pitch_stats['pitch_change_variance']:.2f}")

    # 功率谱密度统计
    freqs, psd = analyzer.compute_power_spectral_density()
    if freqs is not None and psd is not None:
        # 找到功率谱密度的峰值
        peak_idx = np.argmax(psd)
        dominant_freq = freqs[peak_idx]
        max_psd = psd[peak_idx]

        # 计算功率谱密度的统计特性
        psd_mean = np.mean(psd)
        psd_std = np.std(psd)

        print(f"功率谱密度统计:")
        print(f"  主导频率: {dominant_freq:.4f} (归一化)")
        print(f"  最大功率谱密度: {max_psd:.2f}")
        print(f"  平均功率谱密度: {psd_mean:.2f}")
        print(f"  功率谱密度标准差: {psd_std:.2f}")

        # 计算功率谱密度的能量集中度
        total_power = np.sum(psd)
        low_freq_power = np.sum(psd[freqs <= 0.1])  # 低频能量
        mid_freq_power = np.sum(psd[(freqs > 0.1) & (freqs <= 0.3)])  # 中频能量
        high_freq_power = np.sum(psd[freqs > 0.3])  # 高频能量

        print(f"  功率分布:")
        print(f"    低频 (0-0.1): {low_freq_power/total_power*100:.1f}%")
        print(f"    中频 (0.1-0.3): {mid_freq_power/total_power*100:.1f}%")
        print(f"    高频 (0.3-0.5): {high_freq_power/total_power*100:.1f}%")

def main():
    """主函数"""
    print("开始分析MIDI文件...")

    # 创建分析器
    brown_analyzer = MIDIAnalyzer('brown.mid')
    pink_analyzer = MIDIAnalyzer('pink.mid')

    # 解析MIDI文件
    brown_analyzer.parse_midi()
    pink_analyzer.parse_midi()

    # 打印统计信息
    print_statistics(brown_analyzer, "棕色音乐")
    print_statistics(pink_analyzer, "粉色音乐")

    # 生成可视化图表
    print("\n生成可视化图表...")
    plot_pitch_distribution(brown_analyzer, pink_analyzer, "棕色音乐", "粉色音乐")
    plot_note_name_distribution(brown_analyzer, pink_analyzer, "棕色音乐", "粉色音乐")
    plot_duration_distribution(brown_analyzer, pink_analyzer, "棕色音乐", "粉色音乐")
    plot_interval_distribution(brown_analyzer, pink_analyzer, "棕色音乐", "粉色音乐")
    plot_transition_matrix(brown_analyzer, pink_analyzer, "棕色音乐", "粉色音乐")

    # 生成功率谱密度分析图表
    print("\n生成功率谱密度分析图表...")
    plot_power_spectral_density(brown_analyzer, pink_analyzer, "棕色音乐", "粉色音乐")
    plot_pitch_sequence_analysis(brown_analyzer, pink_analyzer, "棕色音乐", "粉色音乐")

    print("\n分析完成！所有图表已保存为PNG文件。")
    print("\n新增图表:")
    print("- power_spectral_density.png: 功率谱密度和自相关函数对比图")
    print("- pitch_sequence_analysis.png: 音高序列和音高变化分析图")

if __name__ == "__main__":
    main()
