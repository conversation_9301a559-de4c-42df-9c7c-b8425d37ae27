import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import librosa
import librosa.display
from scipy import signal
from scipy.fft import fft, fftfreq
import soundfile as sf

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class AudioSpectrumAnalyzer:
    def __init__(self, audio_file):
        self.audio_file = audio_file
        self.audio_data = None
        self.sample_rate = None
        self.duration = None

    def load_audio(self):
        """加载音频文件"""
        try:
            self.audio_data, self.sample_rate = librosa.load(self.audio_file, sr=None)
            self.duration = len(self.audio_data) / self.sample_rate
            print(f"成功加载音频文件: {self.audio_file}")
            print(f"采样率: {self.sample_rate} Hz")
            print(f"时长: {self.duration:.2f} 秒")
            print(f"音频数据长度: {len(self.audio_data)} 样本")
            return True
        except Exception as e:
            print(f"加载音频文件失败: {e}")
            return False

    def compute_spectrum(self):
        """计算频谱"""
        if self.audio_data is None:
            print("请先加载音频文件")
            return None, None

        # 计算FFT
        fft_data = fft(self.audio_data)
        freqs = fftfreq(len(self.audio_data), 1/self.sample_rate)

        # 只取正频率部分
        positive_freqs = freqs[:len(freqs)//2]
        positive_fft = np.abs(fft_data[:len(fft_data)//2])

        return positive_freqs, positive_fft

    def compute_stft(self, n_fft=2048, hop_length=512):
        """计算短时傅里叶变换（STFT）"""
        if self.audio_data is None:
            print("请先加载音频文件")
            return None, None, None

        # 计算STFT
        stft_data = librosa.stft(self.audio_data, n_fft=n_fft, hop_length=hop_length)
        magnitude = np.abs(stft_data)

        # 转换为dB
        magnitude_db = librosa.amplitude_to_db(magnitude, ref=np.max)

        # 频率和时间轴
        freqs = librosa.fft_frequencies(sr=self.sample_rate, n_fft=n_fft)
        times = librosa.frames_to_time(np.arange(magnitude.shape[1]),
                                       sr=self.sample_rate, hop_length=hop_length)

        return freqs, times, magnitude_db

    def find_dominant_frequencies(self, freqs, magnitude, num_peaks=10):
        """找到主要频率成分"""
        # 限制频率范围到可听范围
        freq_mask = (freqs >= 20) & (freqs <= 8000)
        freqs_filtered = freqs[freq_mask]
        magnitude_filtered = magnitude[freq_mask]

        # 找到峰值
        peaks, properties = signal.find_peaks(magnitude_filtered,
                                            height=np.max(magnitude_filtered)*0.1,
                                            distance=20)

        # 按幅度排序，取前num_peaks个
        peak_heights = magnitude_filtered[peaks]
        sorted_indices = np.argsort(peak_heights)[::-1][:num_peaks]

        dominant_freqs = []
        for idx in sorted_indices:
            peak_idx = peaks[idx]
            freq = freqs_filtered[peak_idx]
            amplitude = magnitude_filtered[peak_idx]

            # 将频率转换为最接近的音符
            midi_note = 69 + 12 * np.log2(freq / 440)
            note_names = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
            note_name = note_names[int(round(midi_note)) % 12]
            octave = int(round(midi_note)) // 12 - 1

            dominant_freqs.append({
                'frequency': freq,
                'amplitude': amplitude,
                'note': f"{note_name}{octave}",
                'midi_note': round(midi_note)
            })

        return dominant_freqs

    def compute_spectral_features(self, freqs, magnitude):
        """计算频谱特征"""
        # 限制到可听范围
        freq_mask = (freqs >= 20) & (freqs <= 8000)
        freqs_filtered = freqs[freq_mask]
        magnitude_filtered = magnitude[freq_mask]

        # 频谱重心
        spectral_centroid = np.sum(freqs_filtered * magnitude_filtered) / np.sum(magnitude_filtered)

        # 频谱带宽
        spectral_bandwidth = np.sqrt(np.sum(((freqs_filtered - spectral_centroid) ** 2) * magnitude_filtered) / np.sum(magnitude_filtered))

        # 频谱滚降点（90%能量所在频率）
        cumulative_energy = np.cumsum(magnitude_filtered)
        total_energy = cumulative_energy[-1]
        rolloff_idx = np.where(cumulative_energy >= 0.9 * total_energy)[0][0]
        spectral_rolloff = freqs_filtered[rolloff_idx]

        # 频谱平坦度（谐波与噪声比的度量）
        geometric_mean = np.exp(np.mean(np.log(magnitude_filtered + 1e-10)))
        arithmetic_mean = np.mean(magnitude_filtered)
        spectral_flatness = geometric_mean / arithmetic_mean

        # 频率能量分布
        low_freq_energy = np.sum(magnitude_filtered[(freqs_filtered >= 20) & (freqs_filtered <= 500)])
        mid_freq_energy = np.sum(magnitude_filtered[(freqs_filtered > 500) & (freqs_filtered <= 2000)])
        high_freq_energy = np.sum(magnitude_filtered[(freqs_filtered > 2000) & (freqs_filtered <= 8000)])
        total_energy = low_freq_energy + mid_freq_energy + high_freq_energy

        return {
            'spectral_centroid': spectral_centroid,
            'spectral_bandwidth': spectral_bandwidth,
            'spectral_rolloff': spectral_rolloff,
            'spectral_flatness': spectral_flatness,
            'low_freq_ratio': low_freq_energy / total_energy,
            'mid_freq_ratio': mid_freq_energy / total_energy,
            'high_freq_ratio': high_freq_energy / total_energy
        }

def plot_waveform_comparison(analyzer1, analyzer2, title1, title2):
    """绘制波形对比图"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 8))

    # 棕色音乐波形
    time1 = np.linspace(0, analyzer1.duration, len(analyzer1.audio_data))
    ax1.plot(time1, analyzer1.audio_data, color='brown', alpha=0.7, linewidth=0.5)
    ax1.set_title(f'{title1} - 时域波形')
    ax1.set_xlabel('时间 (秒)')
    ax1.set_ylabel('振幅')
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(0, min(10, analyzer1.duration))  # 只显示前10秒

    # 粉色音乐波形
    time2 = np.linspace(0, analyzer2.duration, len(analyzer2.audio_data))
    ax2.plot(time2, analyzer2.audio_data, color='pink', alpha=0.7, linewidth=0.5)
    ax2.set_title(f'{title2} - 时域波形')
    ax2.set_xlabel('时间 (秒)')
    ax2.set_ylabel('振幅')
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(0, min(10, analyzer2.duration))  # 只显示前10秒

    plt.tight_layout()
    plt.savefig('audio_waveform_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_spectrum_comparison(analyzer1, analyzer2, title1, title2):
    """绘制频谱对比图"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))

    # 计算频谱
    freqs1, magnitude1 = analyzer1.compute_spectrum()
    freqs2, magnitude2 = analyzer2.compute_spectrum()

    # 限制频率范围
    freq_mask1 = (freqs1 >= 20) & (freqs1 <= 8000)
    freq_mask2 = (freqs2 >= 20) & (freqs2 <= 8000)

    # 棕色音乐频谱
    ax1.semilogy(freqs1[freq_mask1], magnitude1[freq_mask1], color='brown', alpha=0.7, linewidth=1)
    ax1.set_title(f'{title1} - 频谱分析')
    ax1.set_xlabel('频率 (Hz)')
    ax1.set_ylabel('幅度 (对数尺度)')
    ax1.grid(True, alpha=0.3)

    # 粉色音乐频谱
    ax2.semilogy(freqs2[freq_mask2], magnitude2[freq_mask2], color='pink', alpha=0.7, linewidth=1)
    ax2.set_title(f'{title2} - 频谱分析')
    ax2.set_xlabel('频率 (Hz)')
    ax2.set_ylabel('幅度 (对数尺度)')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('audio_spectrum_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

    return freqs1, magnitude1, freqs2, magnitude2

def plot_spectrogram_comparison(analyzer1, analyzer2, title1, title2):
    """绘制频谱图对比"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 计算STFT
    freqs1, times1, magnitude_db1 = analyzer1.compute_stft()
    freqs2, times2, magnitude_db2 = analyzer2.compute_stft()

    # 棕色音乐频谱图
    im1 = ax1.imshow(magnitude_db1, aspect='auto', origin='lower',
                     extent=[times1[0], times1[-1], freqs1[0], freqs1[-1]],
                     cmap='Reds', vmin=-80, vmax=0)
    ax1.set_title(f'{title1} - 频谱图')
    ax1.set_xlabel('时间 (秒)')
    ax1.set_ylabel('频率 (Hz)')
    ax1.set_ylim(0, 4000)  # 限制显示频率范围
    plt.colorbar(im1, ax=ax1, label='幅度 (dB)')

    # 粉色音乐频谱图
    im2 = ax2.imshow(magnitude_db2, aspect='auto', origin='lower',
                     extent=[times2[0], times2[-1], freqs2[0], freqs2[-1]],
                     cmap='RdPu', vmin=-80, vmax=0)
    ax2.set_title(f'{title2} - 频谱图')
    ax2.set_xlabel('时间 (秒)')
    ax2.set_ylabel('频率 (Hz)')
    ax2.set_ylim(0, 4000)  # 限制显示频率范围
    plt.colorbar(im2, ax=ax2, label='幅度 (dB)')

    # 计算并显示频谱重心随时间的变化
    centroid1 = librosa.feature.spectral_centroid(y=analyzer1.audio_data, sr=analyzer1.sample_rate)[0]
    centroid2 = librosa.feature.spectral_centroid(y=analyzer2.audio_data, sr=analyzer2.sample_rate)[0]

    time_frames1 = librosa.frames_to_time(np.arange(len(centroid1)), sr=analyzer1.sample_rate)
    time_frames2 = librosa.frames_to_time(np.arange(len(centroid2)), sr=analyzer2.sample_rate)

    ax3.plot(time_frames1, centroid1, color='brown', alpha=0.7, linewidth=2)
    ax3.set_title(f'{title1} - 频谱重心随时间变化')
    ax3.set_xlabel('时间 (秒)')
    ax3.set_ylabel('频谱重心 (Hz)')
    ax3.grid(True, alpha=0.3)

    ax4.plot(time_frames2, centroid2, color='pink', alpha=0.7, linewidth=2)
    ax4.set_title(f'{title2} - 频谱重心随时间变化')
    ax4.set_xlabel('时间 (秒)')
    ax4.set_ylabel('频谱重心 (Hz)')
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('audio_spectrogram_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

def analyze_audio_characteristics(analyzer1, analyzer2, title1, title2):
    """分析音频特征"""
    print(f"\n🎼 音频频谱分析:")

    # 计算频谱
    freqs1, magnitude1 = analyzer1.compute_spectrum()
    freqs2, magnitude2 = analyzer2.compute_spectrum()

    # 找到主要频率成分
    dom_freqs1 = analyzer1.find_dominant_frequencies(freqs1, magnitude1)
    dom_freqs2 = analyzer2.find_dominant_frequencies(freqs2, magnitude2)

    print(f"\n{title1}主要频率成分:")
    for i, freq_info in enumerate(dom_freqs1[:5]):
        print(f"  {i+1}. {freq_info['frequency']:.1f} Hz ({freq_info['note']}) - 幅度: {freq_info['amplitude']:.0f}")

    print(f"\n{title2}主要频率成分:")
    for i, freq_info in enumerate(dom_freqs2[:5]):
        print(f"  {i+1}. {freq_info['frequency']:.1f} Hz ({freq_info['note']}) - 幅度: {freq_info['amplitude']:.0f}")

    # 计算频谱特征
    features1 = analyzer1.compute_spectral_features(freqs1, magnitude1)
    features2 = analyzer2.compute_spectral_features(freqs2, magnitude2)

    print(f"\n频谱特征对比:")
    print(f"频谱重心 (音色明亮度):")
    print(f"  {title1}: {features1['spectral_centroid']:.1f} Hz")
    print(f"  {title2}: {features2['spectral_centroid']:.1f} Hz")

    print(f"频谱带宽 (音色丰富度):")
    print(f"  {title1}: {features1['spectral_bandwidth']:.1f} Hz")
    print(f"  {title2}: {features2['spectral_bandwidth']:.1f} Hz")

    print(f"频谱滚降点 (90%能量频率):")
    print(f"  {title1}: {features1['spectral_rolloff']:.1f} Hz")
    print(f"  {title2}: {features2['spectral_rolloff']:.1f} Hz")

    print(f"频谱平坦度 (谐波性):")
    print(f"  {title1}: {features1['spectral_flatness']:.4f}")
    print(f"  {title2}: {features2['spectral_flatness']:.4f}")
    print(f"  (值越接近1表示越像噪声，越接近0表示越像纯音)")

    print(f"\n频率能量分布:")
    print(f"{title1}:")
    print(f"  低频 (20-500Hz): {features1['low_freq_ratio']*100:.1f}%")
    print(f"  中频 (500-2000Hz): {features1['mid_freq_ratio']*100:.1f}%")
    print(f"  高频 (2000-8000Hz): {features1['high_freq_ratio']*100:.1f}%")

    print(f"{title2}:")
    print(f"  低频 (20-500Hz): {features2['low_freq_ratio']*100:.1f}%")
    print(f"  中频 (500-2000Hz): {features2['mid_freq_ratio']*100:.1f}%")
    print(f"  高频 (2000-8000Hz): {features2['high_freq_ratio']*100:.1f}%")

    return features1, features2

def generate_audio_analysis_report(analyzer1, analyzer2, features1, features2, title1, title2):
    """生成音频分析报告"""
    print(f"\n📋 音频分析结论:")

    # 音色明亮度对比
    if features1['spectral_centroid'] > features2['spectral_centroid']:
        print(f"1. {title1}的音色更明亮 (频谱重心: {features1['spectral_centroid']:.1f} vs {features2['spectral_centroid']:.1f} Hz)")
    else:
        print(f"1. {title2}的音色更明亮 (频谱重心: {features2['spectral_centroid']:.1f} vs {features1['spectral_centroid']:.1f} Hz)")

    # 音色丰富度对比
    if features1['spectral_bandwidth'] > features2['spectral_bandwidth']:
        print(f"2. {title1}的音色更丰富 (频谱带宽: {features1['spectral_bandwidth']:.1f} vs {features2['spectral_bandwidth']:.1f} Hz)")
    else:
        print(f"2. {title2}的音色更丰富 (频谱带宽: {features2['spectral_bandwidth']:.1f} vs {features1['spectral_bandwidth']:.1f} Hz)")

    # 谐波性对比
    if features1['spectral_flatness'] < features2['spectral_flatness']:
        print(f"3. {title1}更具谐波性 (频谱平坦度: {features1['spectral_flatness']:.4f} vs {features2['spectral_flatness']:.4f})")
    else:
        print(f"3. {title2}更具谐波性 (频谱平坦度: {features2['spectral_flatness']:.4f} vs {features1['spectral_flatness']:.4f})")

    # 能量分布对比
    if features1['high_freq_ratio'] > features2['high_freq_ratio']:
        print(f"4. {title1}的高频能量更丰富 ({features1['high_freq_ratio']*100:.1f}% vs {features2['high_freq_ratio']*100:.1f}%)")
    else:
        print(f"4. {title2}的高频能量更丰富 ({features2['high_freq_ratio']*100:.1f}% vs {features1['high_freq_ratio']*100:.1f}%)")

    # 音频质量信息
    print(f"\n📊 音频质量信息:")
    print(f"{title1}:")
    print(f"  采样率: {analyzer1.sample_rate} Hz")
    print(f"  时长: {analyzer1.duration:.2f} 秒")
    print(f"  动态范围: {np.max(analyzer1.audio_data) - np.min(analyzer1.audio_data):.4f}")

    print(f"{title2}:")
    print(f"  采样率: {analyzer2.sample_rate} Hz")
    print(f"  时长: {analyzer2.duration:.2f} 秒")
    print(f"  动态范围: {np.max(analyzer2.audio_data) - np.min(analyzer2.audio_data):.4f}")

def main():
    """主函数"""
    print("=" * 80)
    print("音频频谱分析")
    print("=" * 80)

    # 创建分析器
    brown_analyzer = AudioSpectrumAnalyzer('brown.wav')
    pink_analyzer = AudioSpectrumAnalyzer('pink.wav')

    # 加载音频文件
    if not brown_analyzer.load_audio():
        print("无法加载棕色音乐文件")
        return

    if not pink_analyzer.load_audio():
        print("无法加载粉色音乐文件")
        return

    # 生成可视化图表
    print("\n生成音频分析图表...")
    plot_waveform_comparison(brown_analyzer, pink_analyzer, "棕色音乐", "粉色音乐")
    plot_spectrum_comparison(brown_analyzer, pink_analyzer, "棕色音乐", "粉色音乐")
    plot_spectrogram_comparison(brown_analyzer, pink_analyzer, "棕色音乐", "粉色音乐")

    # 分析音频特征
    features1, features2 = analyze_audio_characteristics(brown_analyzer, pink_analyzer, "棕色音乐", "粉色音乐")

    # 生成分析报告
    generate_audio_analysis_report(brown_analyzer, pink_analyzer, features1, features2, "棕色音乐", "粉色音乐")

    print(f"\n📁 生成的图表文件:")
    print(f"- audio_waveform_comparison.png: 时域波形对比图")
    print(f"- audio_spectrum_comparison.png: 频域频谱对比图")
    print(f"- audio_spectrogram_comparison.png: 频谱图和频谱重心变化图")

    print("=" * 80)

if __name__ == "__main__":
    main()
