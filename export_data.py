import csv
import json
import numpy as np
from midi_analysis import MIDIAnalyzer

def export_analysis_data():
    """导出分析数据到CSV和JSON文件"""
    print("导出分析数据...")
    
    # 创建分析器
    brown_analyzer = MIDIAnalyzer('brown.mid')
    pink_analyzer = MIDIAnalyzer('pink.mid')
    
    # 解析MIDI文件
    brown_analyzer.parse_midi()
    pink_analyzer.parse_midi()
    
    # 1. 导出音高分布数据
    brown_pitches = brown_analyzer.get_pitch_distribution()
    pink_pitches = pink_analyzer.get_pitch_distribution()
    
    with open('pitch_distribution.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['音高', '棕色音乐概率', '粉色音乐概率'])
        
        all_pitches = set(brown_pitches.keys()) | set(pink_pitches.keys())
        for pitch in sorted(all_pitches):
            brown_prob = brown_pitches.get(pitch, 0)
            pink_prob = pink_pitches.get(pitch, 0)
            writer.writerow([pitch, brown_prob, pink_prob])
    
    # 2. 导出音名分布数据
    brown_names = brown_analyzer.get_note_name_distribution()
    pink_names = pink_analyzer.get_note_name_distribution()
    
    with open('note_name_distribution.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['音名', '棕色音乐概率', '粉色音乐概率'])
        
        note_names = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
        for name in note_names:
            brown_prob = brown_names.get(name, 0)
            pink_prob = pink_names.get(name, 0)
            writer.writerow([name, brown_prob, pink_prob])
    
    # 3. 导出时值分布数据
    brown_durations = brown_analyzer.get_duration_distribution()
    pink_durations = pink_analyzer.get_duration_distribution()
    
    with open('duration_distribution.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['时值(ticks)', '棕色音乐概率', '粉色音乐概率'])
        
        all_durations = set(brown_durations.keys()) | set(pink_durations.keys())
        for duration in sorted(all_durations):
            brown_prob = brown_durations.get(duration, 0)
            pink_prob = pink_durations.get(duration, 0)
            writer.writerow([duration, brown_prob, pink_prob])
    
    # 4. 导出音程分布数据
    brown_intervals = brown_analyzer.get_interval_distribution()
    pink_intervals = pink_analyzer.get_interval_distribution()
    
    with open('interval_distribution.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['音程(半音)', '棕色音乐概率', '粉色音乐概率'])
        
        all_intervals = set(brown_intervals.keys()) | set(pink_intervals.keys())
        for interval in sorted(all_intervals):
            brown_prob = brown_intervals.get(interval, 0)
            pink_prob = pink_intervals.get(interval, 0)
            writer.writerow([interval, brown_prob, pink_prob])
    
    # 5. 导出转移矩阵数据
    brown_matrix = brown_analyzer.get_note_transition_matrix()
    pink_matrix = pink_analyzer.get_note_transition_matrix()
    
    note_names = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
    
    # 棕色音乐转移矩阵
    with open('brown_transition_matrix.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['从\\到'] + note_names)
        for i, from_note in enumerate(note_names):
            row = [from_note] + [f"{brown_matrix[i][j]:.4f}" for j in range(12)]
            writer.writerow(row)
    
    # 粉色音乐转移矩阵
    with open('pink_transition_matrix.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['从\\到'] + note_names)
        for i, from_note in enumerate(note_names):
            row = [from_note] + [f"{pink_matrix[i][j]:.4f}" for j in range(12)]
            writer.writerow(row)
    
    # 6. 导出原始音符数据
    with open('brown_notes.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['音高', '时值(ticks)', '开始时间(ticks)', '音名'])
        for note in brown_analyzer.notes:
            pitch, duration, start_time = note
            note_name = brown_analyzer.note_names[pitch % 12]
            writer.writerow([pitch, duration, start_time, note_name])
    
    with open('pink_notes.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['音高', '时值(ticks)', '开始时间(ticks)', '音名'])
        for note in pink_analyzer.notes:
            pitch, duration, start_time = note
            note_name = pink_analyzer.note_names[pitch % 12]
            writer.writerow([pitch, duration, start_time, note_name])
    
    # 7. 导出JSON格式的汇总数据
    summary_data = {
        'brown_music': {
            'total_notes': len(brown_analyzer.notes),
            'pitch_range': [min(brown_pitches.keys()), max(brown_pitches.keys())],
            'note_name_distribution': brown_names,
            'duration_types': len(brown_durations),
            'interval_range': [min(brown_intervals.keys()), max(brown_intervals.keys())],
            'transition_matrix': brown_matrix.tolist()
        },
        'pink_music': {
            'total_notes': len(pink_analyzer.notes),
            'pitch_range': [min(pink_pitches.keys()), max(pink_pitches.keys())],
            'note_name_distribution': pink_names,
            'duration_types': len(pink_durations),
            'interval_range': [min(pink_intervals.keys()), max(pink_intervals.keys())],
            'transition_matrix': pink_matrix.tolist()
        }
    }
    
    with open('analysis_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary_data, f, ensure_ascii=False, indent=2)
    
    print("数据导出完成！")
    print("生成的文件:")
    print("- pitch_distribution.csv: 音高分布数据")
    print("- note_name_distribution.csv: 音名分布数据")
    print("- duration_distribution.csv: 时值分布数据")
    print("- interval_distribution.csv: 音程分布数据")
    print("- brown_transition_matrix.csv: 棕色音乐转移矩阵")
    print("- pink_transition_matrix.csv: 粉色音乐转移矩阵")
    print("- brown_notes.csv: 棕色音乐原始音符数据")
    print("- pink_notes.csv: 粉色音乐原始音符数据")
    print("- analysis_summary.json: 分析结果汇总")

if __name__ == "__main__":
    export_analysis_data()
