import mido
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from midi_analysis import MIDIAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_final_improvement_report():
    """生成最终改进报告"""
    print("=" * 80)
    print("MIDI音乐分析 - 最终改进报告")
    print("=" * 80)
    
    # 创建分析器
    brown_analyzer = MIDIAnalyzer('brown.mid')
    pink_analyzer = MIDIAnalyzer('pink.mid')
    
    # 解析MIDI文件
    brown_analyzer.parse_midi()
    pink_analyzer.parse_midi()
    
    print(f"\n📊 改进功能验证:")
    print(f"✅ 音高分布图改进：横轴现在使用音名标识（如C4、D4）而非MIDI数字")
    print(f"✅ 时值分布图改进：使用直方图显示，横轴标注音符名称（四分音符、八分音符等）")
    print(f"✅ 统计信息改进：显示音名格式的音高范围和详细的时值分布")
    
    print(f"\n🎵 改进后的音高分析:")
    brown_pitches = brown_analyzer.get_pitch_distribution()
    pink_pitches = pink_analyzer.get_pitch_distribution()
    
    brown_min_pitch = min(brown_pitches.keys())
    brown_max_pitch = max(brown_pitches.keys())
    brown_most_common = max(brown_pitches, key=brown_pitches.get)
    
    pink_min_pitch = min(pink_pitches.keys())
    pink_max_pitch = max(pink_pitches.keys())
    pink_most_common = max(pink_pitches, key=pink_pitches.get)
    
    print(f"棕色音乐:")
    print(f"  音高范围: {brown_analyzer.midi_to_note_name(brown_min_pitch)} - {brown_analyzer.midi_to_note_name(brown_max_pitch)}")
    print(f"  最常见音高: {brown_analyzer.midi_to_note_name(brown_most_common)} (概率: {brown_pitches[brown_most_common]:.3f})")
    print(f"  音域宽度: {brown_max_pitch - brown_min_pitch} 半音")
    
    print(f"粉色音乐:")
    print(f"  音高范围: {pink_analyzer.midi_to_note_name(pink_min_pitch)} - {pink_analyzer.midi_to_note_name(pink_max_pitch)}")
    print(f"  最常见音高: {pink_analyzer.midi_to_note_name(pink_most_common)} (概率: {pink_pitches[pink_most_common]:.3f})")
    print(f"  音域宽度: {pink_max_pitch - pink_min_pitch} 半音")
    
    print(f"\n⏱️ 改进后的时值分析:")
    brown_duration_by_value = brown_analyzer.get_duration_by_note_value()
    pink_duration_by_value = pink_analyzer.get_duration_by_note_value()
    
    print(f"棕色音乐时值分布:")
    for note_value, prob in sorted(brown_duration_by_value.items(), key=lambda x: x[1], reverse=True):
        print(f"  {note_value}: {prob:.3f} ({prob*100:.1f}%)")
    
    print(f"粉色音乐时值分布:")
    for note_value, prob in sorted(pink_duration_by_value.items(), key=lambda x: x[1], reverse=True):
        print(f"  {note_value}: {prob:.3f} ({prob*100:.1f}%)")
    
    print(f"\n📈 改进前后对比:")
    print(f"改进前:")
    print(f"  - 音高分布图使用MIDI数字（50, 51, 52...），不直观")
    print(f"  - 时值分布图使用散点图和ticks数值，难以理解")
    print(f"  - 统计信息显示原始数值，缺乏音乐意义")
    
    print(f"改进后:")
    print(f"  - 音高分布图使用音名（D3, D#3, E3...），直观易懂")
    print(f"  - 时值分布图使用直方图和音符名称，符合音乐习惯")
    print(f"  - 统计信息显示音乐术语，便于理解和分析")
    
    print(f"\n🎼 音乐特征对比:")
    
    # 音域对比
    brown_range = brown_max_pitch - brown_min_pitch
    pink_range = pink_max_pitch - pink_min_pitch
    
    if brown_range > pink_range:
        print(f"  音域宽度: 棕色音乐更宽广 ({brown_range} vs {pink_range} 半音)")
    elif brown_range < pink_range:
        print(f"  音域宽度: 粉色音乐更宽广 ({pink_range} vs {brown_range} 半音)")
    else:
        print(f"  音域宽度: 两段音乐相同 ({brown_range} 半音)")
    
    # 时值特征对比
    brown_main_duration = max(brown_duration_by_value, key=brown_duration_by_value.get)
    pink_main_duration = max(pink_duration_by_value, key=pink_duration_by_value.get)
    
    print(f"  主要时值: 棕色音乐以{brown_main_duration}为主({brown_duration_by_value[brown_main_duration]*100:.1f}%)")
    print(f"           粉色音乐以{pink_main_duration}为主({pink_duration_by_value[pink_main_duration]*100:.1f}%)")
    
    # 时值多样性
    brown_diversity = len(brown_duration_by_value)
    pink_diversity = len(pink_duration_by_value)
    
    if brown_diversity > pink_diversity:
        print(f"  时值多样性: 棕色音乐更丰富 ({brown_diversity} vs {pink_diversity} 种)")
    elif brown_diversity < pink_diversity:
        print(f"  时值多样性: 粉色音乐更丰富 ({pink_diversity} vs {brown_diversity} 种)")
    else:
        print(f"  时值多样性: 两段音乐相同 ({brown_diversity} 种)")
    
    print(f"\n📁 生成的改进图表:")
    print(f"- pitch_distribution.png: 改进的音高分布图（使用音名标识）")
    print(f"- duration_distribution.png: 改进的时值分布图（使用音符名称和直方图）")
    print(f"- note_name_distribution.png: 音名分布对比图")
    print(f"- interval_distribution.png: 音程分布对比图")
    print(f"- transition_matrix.png: 音名转移概率矩阵热力图")
    
    print(f"\n🎯 改进效果总结:")
    print(f"1. 可视化更直观：使用音乐术语替代技术数值")
    print(f"2. 分析更专业：符合音乐理论和实践习惯")
    print(f"3. 理解更容易：非专业人士也能理解图表含义")
    print(f"4. 信息更丰富：提供更多音乐层面的统计信息")
    
    print("=" * 80)

if __name__ == "__main__":
    generate_final_improvement_report()
