import mido
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
from midi_analysis import MIDIAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_comprehensive_report():
    """生成综合分析报告"""
    print("=" * 60)
    print("MIDI音乐统计分析报告")
    print("=" * 60)
    
    # 创建分析器
    brown_analyzer = MIDIAnalyzer('brown.mid')
    pink_analyzer = MIDIAnalyzer('pink.mid')
    
    # 解析MIDI文件
    brown_analyzer.parse_midi()
    pink_analyzer.parse_midi()
    
    print(f"\n📊 基本信息:")
    print(f"棕色音乐: {len(brown_analyzer.notes)} 个音符")
    print(f"粉色音乐: {len(pink_analyzer.notes)} 个音符")
    
    # 1. 音高分析
    print(f"\n🎵 音高分析:")
    brown_pitches = brown_analyzer.get_pitch_distribution()
    pink_pitches = pink_analyzer.get_pitch_distribution()
    
    print(f"棕色音乐音高范围: {min(brown_pitches.keys())} - {max(brown_pitches.keys())}")
    print(f"粉色音乐音高范围: {min(pink_pitches.keys())} - {max(pink_pitches.keys())}")
    
    # 2. 音名分析
    print(f"\n🎼 音名分析:")
    brown_names = brown_analyzer.get_note_name_distribution()
    pink_names = pink_analyzer.get_note_name_distribution()
    
    print("棕色音乐音名分布:")
    for name in ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']:
        prob = brown_names.get(name, 0)
        print(f"  {name}: {prob:.3f}")
    
    print("粉色音乐音名分布:")
    for name in ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']:
        prob = pink_names.get(name, 0)
        print(f"  {name}: {prob:.3f}")
    
    # 3. 时值分析
    print(f"\n⏱️ 时值分析:")
    brown_durations = brown_analyzer.get_duration_distribution()
    pink_durations = pink_analyzer.get_duration_distribution()
    
    print(f"棕色音乐时值种类: {len(brown_durations)} 种")
    print(f"粉色音乐时值种类: {len(pink_durations)} 种")
    
    # 4. 音程分析
    print(f"\n🎯 音程分析:")
    brown_intervals = brown_analyzer.get_interval_distribution()
    pink_intervals = pink_analyzer.get_interval_distribution()
    
    print(f"棕色音乐音程范围: {min(brown_intervals.keys())} 到 {max(brown_intervals.keys())} 半音")
    print(f"粉色音乐音程范围: {min(pink_intervals.keys())} 到 {max(pink_intervals.keys())} 半音")
    
    # 5. 转移矩阵分析
    print(f"\n🔄 音名转移分析:")
    brown_matrix = brown_analyzer.get_note_transition_matrix()
    pink_matrix = pink_analyzer.get_note_transition_matrix()
    
    # 计算转移矩阵的熵（衡量随机性）
    def calculate_entropy(matrix):
        entropy = 0
        for i in range(12):
            for j in range(12):
                if matrix[i][j] > 0:
                    entropy -= matrix[i][j] * np.log2(matrix[i][j])
        return entropy
    
    brown_entropy = calculate_entropy(brown_matrix)
    pink_entropy = calculate_entropy(pink_matrix)
    
    print(f"棕色音乐转移熵: {brown_entropy:.3f} bits")
    print(f"粉色音乐转移熵: {pink_entropy:.3f} bits")
    print(f"(熵值越高表示音名转移越随机)")
    
    # 6. 对比分析
    print(f"\n📈 对比分析:")
    
    # 音高集中度
    brown_pitch_entropy = -sum(p * np.log2(p) for p in brown_pitches.values() if p > 0)
    pink_pitch_entropy = -sum(p * np.log2(p) for p in pink_pitches.values() if p > 0)
    
    print(f"音高分布熵:")
    print(f"  棕色音乐: {brown_pitch_entropy:.3f} bits")
    print(f"  粉色音乐: {pink_pitch_entropy:.3f} bits")
    
    # 音名集中度
    brown_name_entropy = -sum(p * np.log2(p) for p in brown_names.values() if p > 0)
    pink_name_entropy = -sum(p * np.log2(p) for p in pink_names.values() if p > 0)
    
    print(f"音名分布熵:")
    print(f"  棕色音乐: {brown_name_entropy:.3f} bits")
    print(f"  粉色音乐: {pink_name_entropy:.3f} bits")
    
    # 时值集中度
    brown_duration_entropy = -sum(p * np.log2(p) for p in brown_durations.values() if p > 0)
    pink_duration_entropy = -sum(p * np.log2(p) for p in pink_durations.values() if p > 0)
    
    print(f"时值分布熵:")
    print(f"  棕色音乐: {brown_duration_entropy:.3f} bits")
    print(f"  粉色音乐: {pink_duration_entropy:.3f} bits")
    
    print(f"\n📋 结论:")
    print(f"1. 音高范围: 棕色音乐更宽广 ({max(brown_pitches.keys()) - min(brown_pitches.keys())} vs {max(pink_pitches.keys()) - min(pink_pitches.keys())} 半音)")
    print(f"2. 音名分布: {'棕色' if brown_name_entropy > pink_name_entropy else '粉色'}音乐的音名分布更均匀")
    print(f"3. 时值多样性: {'棕色' if len(brown_durations) > len(pink_durations) else '粉色'}音乐的时值种类更多")
    print(f"4. 音程跳跃: {'棕色' if max(brown_intervals.keys()) - min(brown_intervals.keys()) > max(pink_intervals.keys()) - min(pink_intervals.keys()) else '粉色'}音乐的音程跳跃更大")
    print(f"5. 转移随机性: {'棕色' if brown_entropy > pink_entropy else '粉色'}音乐的音名转移更随机")
    
    print(f"\n📁 生成的文件:")
    print(f"- pitch_distribution.png: 音高分布对比图")
    print(f"- note_name_distribution.png: 音名分布对比图")
    print(f"- duration_distribution.png: 时值分布对比图")
    print(f"- interval_distribution.png: 音程分布对比图")
    print(f"- transition_matrix.png: 音名转移概率矩阵热力图")
    
    print("=" * 60)

if __name__ == "__main__":
    generate_comprehensive_report()
