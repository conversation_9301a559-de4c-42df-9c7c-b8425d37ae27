import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from midi_analysis import MIDIAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_power_spectrum_analysis_report():
    """生成功率谱密度分析专题报告"""
    print("=" * 80)
    print("MIDI音乐功率谱密度分析专题报告")
    print("=" * 80)
    
    # 创建分析器
    brown_analyzer = MIDIAnalyzer('brown.mid')
    pink_analyzer = MIDIAnalyzer('pink.mid')
    
    # 解析MIDI文件
    brown_analyzer.parse_midi()
    pink_analyzer.parse_midi()
    
    print(f"\n📊 功率谱密度分析原理:")
    print(f"功率谱密度(PSD)是通过以下步骤计算的：")
    print(f"1. 将MIDI音乐视为音高的时间序列")
    print(f"2. 计算音高序列的自相关函数")
    print(f"3. 对自相关函数进行傅里叶变换得到功率谱密度")
    print(f"4. 功率谱密度反映了音高序列中不同频率成分的能量分布")
    
    # 获取音高序列
    brown_pitch_seq = brown_analyzer.get_pitch_sequence()
    pink_pitch_seq = pink_analyzer.get_pitch_sequence()
    
    print(f"\n🎵 音高序列基本信息:")
    print(f"棕色音乐:")
    print(f"  序列长度: {len(brown_pitch_seq)} 个音符")
    print(f"  音高范围: {brown_analyzer.midi_to_note_name(np.min(brown_pitch_seq))} - {brown_analyzer.midi_to_note_name(np.max(brown_pitch_seq))}")
    print(f"  平均音高: {np.mean(brown_pitch_seq):.1f} ({brown_analyzer.midi_to_note_name(int(np.mean(brown_pitch_seq)))})")
    print(f"  音高标准差: {np.std(brown_pitch_seq):.2f} 半音")
    
    print(f"粉色音乐:")
    print(f"  序列长度: {len(pink_pitch_seq)} 个音符")
    print(f"  音高范围: {pink_analyzer.midi_to_note_name(np.min(pink_pitch_seq))} - {pink_analyzer.midi_to_note_name(np.max(pink_pitch_seq))}")
    print(f"  平均音高: {np.mean(pink_pitch_seq):.1f} ({pink_analyzer.midi_to_note_name(int(np.mean(pink_pitch_seq)))})")
    print(f"  音高标准差: {np.std(pink_pitch_seq):.2f} 半音")
    
    # 计算自相关函数
    print(f"\n🔄 自相关函数分析:")
    max_lag = 20  # 分析前20个延迟
    
    brown_autocorr = brown_analyzer.compute_autocorrelation(brown_pitch_seq, max_lag)
    pink_autocorr = pink_analyzer.compute_autocorrelation(pink_pitch_seq, max_lag)
    
    print(f"棕色音乐自相关特性:")
    print(f"  延迟1的自相关: {brown_autocorr[1]:.3f}")
    print(f"  延迟2的自相关: {brown_autocorr[2]:.3f}")
    print(f"  延迟3的自相关: {brown_autocorr[3]:.3f}")
    
    # 找到第一个负值延迟
    brown_first_negative = np.where(brown_autocorr < 0)[0]
    if len(brown_first_negative) > 0:
        print(f"  首次负相关延迟: {brown_first_negative[0]} 个音符")
    
    print(f"粉色音乐自相关特性:")
    print(f"  延迟1的自相关: {pink_autocorr[1]:.3f}")
    print(f"  延迟2的自相关: {pink_autocorr[2]:.3f}")
    print(f"  延迟3的自相关: {pink_autocorr[3]:.3f}")
    
    # 找到第一个负值延迟
    pink_first_negative = np.where(pink_autocorr < 0)[0]
    if len(pink_first_negative) > 0:
        print(f"  首次负相关延迟: {pink_first_negative[0]} 个音符")
    
    # 计算功率谱密度
    print(f"\n📈 功率谱密度分析:")
    brown_freqs, brown_psd = brown_analyzer.compute_power_spectral_density()
    pink_freqs, pink_psd = pink_analyzer.compute_power_spectral_density()
    
    # 找到主要频率成分
    brown_peak_idx = np.argmax(brown_psd)
    brown_dominant_freq = brown_freqs[brown_peak_idx]
    brown_max_psd = brown_psd[brown_peak_idx]
    
    pink_peak_idx = np.argmax(pink_psd)
    pink_dominant_freq = pink_freqs[pink_peak_idx]
    pink_max_psd = pink_psd[pink_peak_idx]
    
    print(f"棕色音乐功率谱密度特征:")
    print(f"  主导频率: {brown_dominant_freq:.4f} (归一化)")
    print(f"  对应周期: {1/brown_dominant_freq:.1f} 个音符" if brown_dominant_freq > 0 else "  对应周期: 无限大")
    print(f"  最大功率谱密度: {brown_max_psd:.2f}")
    print(f"  平均功率谱密度: {np.mean(brown_psd):.2f}")
    print(f"  功率谱密度标准差: {np.std(brown_psd):.2f}")
    
    print(f"粉色音乐功率谱密度特征:")
    print(f"  主导频率: {pink_dominant_freq:.4f} (归一化)")
    print(f"  对应周期: {1/pink_dominant_freq:.1f} 个音符" if pink_dominant_freq > 0 else "  对应周期: 无限大")
    print(f"  最大功率谱密度: {pink_max_psd:.2f}")
    print(f"  平均功率谱密度: {np.mean(pink_psd):.2f}")
    print(f"  功率谱密度标准差: {np.std(pink_psd):.2f}")
    
    # 功率分布分析
    print(f"\n🎯 功率分布分析:")
    
    # 棕色音乐功率分布
    brown_total_power = np.sum(brown_psd)
    brown_low_power = np.sum(brown_psd[brown_freqs <= 0.1])
    brown_mid_power = np.sum(brown_psd[(brown_freqs > 0.1) & (brown_freqs <= 0.3)])
    brown_high_power = np.sum(brown_psd[brown_freqs > 0.3])
    
    print(f"棕色音乐功率分布:")
    print(f"  低频 (0-0.1): {brown_low_power/brown_total_power*100:.1f}%")
    print(f"  中频 (0.1-0.3): {brown_mid_power/brown_total_power*100:.1f}%")
    print(f"  高频 (0.3-0.5): {brown_high_power/brown_total_power*100:.1f}%")
    
    # 粉色音乐功率分布
    pink_total_power = np.sum(pink_psd)
    pink_low_power = np.sum(pink_psd[pink_freqs <= 0.1])
    pink_mid_power = np.sum(pink_psd[(pink_freqs > 0.1) & (pink_freqs <= 0.3)])
    pink_high_power = np.sum(pink_psd[pink_freqs > 0.3])
    
    print(f"粉色音乐功率分布:")
    print(f"  低频 (0-0.1): {pink_low_power/pink_total_power*100:.1f}%")
    print(f"  中频 (0.1-0.3): {pink_mid_power/pink_total_power*100:.1f}%")
    print(f"  高频 (0.3-0.5): {pink_high_power/pink_total_power*100:.1f}%")
    
    # 音高变化分析
    print(f"\n📊 音高变化模式分析:")
    brown_pitch_diff = np.diff(brown_pitch_seq)
    pink_pitch_diff = np.diff(pink_pitch_seq)
    
    print(f"棕色音乐音高变化特征:")
    print(f"  平均音高变化: {np.mean(brown_pitch_diff):.2f} 半音")
    print(f"  音高变化标准差: {np.std(brown_pitch_diff):.2f} 半音")
    print(f"  最大上行跳跃: {np.max(brown_pitch_diff):.0f} 半音")
    print(f"  最大下行跳跃: {np.min(brown_pitch_diff):.0f} 半音")
    print(f"  音高变化方差: {np.var(brown_pitch_diff):.2f}")
    
    print(f"粉色音乐音高变化特征:")
    print(f"  平均音高变化: {np.mean(pink_pitch_diff):.2f} 半音")
    print(f"  音高变化标准差: {np.std(pink_pitch_diff):.2f} 半音")
    print(f"  最大上行跳跃: {np.max(pink_pitch_diff):.0f} 半音")
    print(f"  最大下行跳跃: {np.min(pink_pitch_diff):.0f} 半音")
    print(f"  音高变化方差: {np.var(pink_pitch_diff):.2f}")
    
    # 对比分析
    print(f"\n📋 功率谱密度对比结论:")
    
    if brown_dominant_freq < pink_dominant_freq:
        print(f"1. 棕色音乐具有更低的主导频率，表明其音高模式具有更长的周期性")
    else:
        print(f"1. 粉色音乐具有更低的主导频率，表明其音高模式具有更长的周期性")
    
    if brown_max_psd > pink_max_psd:
        print(f"2. 棕色音乐的最大功率谱密度更高，表明其主导模式更强")
    else:
        print(f"2. 粉色音乐的最大功率谱密度更高，表明其主导模式更强")
    
    if brown_low_power/brown_total_power > pink_low_power/pink_total_power:
        print(f"3. 棕色音乐的低频功率更集中，表明其具有更强的长期趋势")
    else:
        print(f"3. 粉色音乐的低频功率更集中，表明其具有更强的长期趋势")
    
    if np.var(brown_pitch_diff) < np.var(pink_pitch_diff):
        print(f"4. 棕色音乐的音高变化更规律（方差较小）")
    else:
        print(f"4. 粉色音乐的音高变化更规律（方差较小）")
    
    print(f"\n🎼 音乐学意义:")
    print(f"1. 功率谱密度分析揭示了音乐中音高序列的周期性和规律性")
    print(f"2. 低频成分反映音乐的长期结构和整体走向")
    print(f"3. 高频成分反映音乐的局部变化和细节特征")
    print(f"4. 自相关函数显示了音高模式的重复性和相关性")
    print(f"5. 这种分析方法可以用于音乐风格识别和作曲技法研究")
    
    print(f"\n📁 生成的图表文件:")
    print(f"- power_spectral_density.png: 功率谱密度和自相关函数对比图")
    print(f"- pitch_sequence_analysis.png: 音高序列和音高变化分析图")
    
    print("=" * 80)

if __name__ == "__main__":
    generate_power_spectrum_analysis_report()
