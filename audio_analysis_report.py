import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from audio_spectrum_analysis import AudioSpectrumAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_comprehensive_audio_report():
    """生成综合音频分析报告"""
    print("=" * 80)
    print("钢琴音色音频频谱分析综合报告")
    print("=" * 80)
    
    # 创建分析器
    brown_analyzer = AudioSpectrumAnalyzer('brown.wav')
    pink_analyzer = AudioSpectrumAnalyzer('pink.wav')
    
    # 加载音频文件
    if not brown_analyzer.load_audio():
        print("无法加载棕色音乐文件")
        return
    
    if not pink_analyzer.load_audio():
        print("无法加载粉色音乐文件")
        return
    
    print(f"\n📊 音频基本信息对比:")
    print(f"棕色音乐:")
    print(f"  文件: brown.wav")
    print(f"  采样率: {brown_analyzer.sample_rate} Hz")
    print(f"  时长: {brown_analyzer.duration:.2f} 秒")
    print(f"  音频数据长度: {len(brown_analyzer.audio_data):,} 样本")
    print(f"  动态范围: {np.max(brown_analyzer.audio_data) - np.min(brown_analyzer.audio_data):.4f}")
    
    print(f"粉色音乐:")
    print(f"  文件: pink.wav")
    print(f"  采样率: {pink_analyzer.sample_rate} Hz")
    print(f"  时长: {pink_analyzer.duration:.2f} 秒")
    print(f"  音频数据长度: {len(pink_analyzer.audio_data):,} 样本")
    print(f"  动态范围: {np.max(pink_analyzer.audio_data) - np.min(pink_analyzer.audio_data):.4f}")
    
    # 计算频谱
    freqs1, magnitude1 = brown_analyzer.compute_spectrum()
    freqs2, magnitude2 = pink_analyzer.compute_spectrum()
    
    # 找到主要频率成分
    dom_freqs1 = brown_analyzer.find_dominant_frequencies(freqs1, magnitude1, num_peaks=10)
    dom_freqs2 = pink_analyzer.find_dominant_frequencies(freqs2, magnitude2, num_peaks=10)
    
    print(f"\n🎵 主要频率成分分析:")
    print(f"棕色音乐前10个主要频率:")
    for i, freq_info in enumerate(dom_freqs1):
        print(f"  {i+1:2d}. {freq_info['frequency']:6.1f} Hz ({freq_info['note']:>3s}) - 幅度: {freq_info['amplitude']:6.0f}")
    
    print(f"粉色音乐前10个主要频率:")
    for i, freq_info in enumerate(dom_freqs2):
        print(f"  {i+1:2d}. {freq_info['frequency']:6.1f} Hz ({freq_info['note']:>3s}) - 幅度: {freq_info['amplitude']:6.0f}")
    
    # 计算频谱特征
    features1 = brown_analyzer.compute_spectral_features(freqs1, magnitude1)
    features2 = pink_analyzer.compute_spectral_features(freqs2, magnitude2)
    
    print(f"\n🎼 频谱特征详细对比:")
    print(f"{'特征':<20} {'棕色音乐':<15} {'粉色音乐':<15} {'差异':<15}")
    print(f"{'-'*65}")
    
    centroid_diff = features2['spectral_centroid'] - features1['spectral_centroid']
    print(f"{'频谱重心 (Hz)':<20} {features1['spectral_centroid']:<15.1f} {features2['spectral_centroid']:<15.1f} {centroid_diff:+.1f}")
    
    bandwidth_diff = features2['spectral_bandwidth'] - features1['spectral_bandwidth']
    print(f"{'频谱带宽 (Hz)':<20} {features1['spectral_bandwidth']:<15.1f} {features2['spectral_bandwidth']:<15.1f} {bandwidth_diff:+.1f}")
    
    rolloff_diff = features2['spectral_rolloff'] - features1['spectral_rolloff']
    print(f"{'频谱滚降 (Hz)':<20} {features1['spectral_rolloff']:<15.1f} {features2['spectral_rolloff']:<15.1f} {rolloff_diff:+.1f}")
    
    flatness_diff = features2['spectral_flatness'] - features1['spectral_flatness']
    print(f"{'频谱平坦度':<20} {features1['spectral_flatness']:<15.4f} {features2['spectral_flatness']:<15.4f} {flatness_diff:+.4f}")
    
    print(f"\n🔊 频率能量分布详细对比:")
    print(f"{'频段':<15} {'棕色音乐':<15} {'粉色音乐':<15} {'差异':<15}")
    print(f"{'-'*60}")
    
    low_diff = features2['low_freq_ratio'] - features1['low_freq_ratio']
    print(f"{'低频 (20-500Hz)':<15} {features1['low_freq_ratio']*100:<15.1f}% {features2['low_freq_ratio']*100:<15.1f}% {low_diff*100:+.1f}%")
    
    mid_diff = features2['mid_freq_ratio'] - features1['mid_freq_ratio']
    print(f"{'中频 (500-2000Hz)':<15} {features1['mid_freq_ratio']*100:<15.1f}% {features2['mid_freq_ratio']*100:<15.1f}% {mid_diff*100:+.1f}%")
    
    high_diff = features2['high_freq_ratio'] - features1['high_freq_ratio']
    print(f"{'高频 (2000-8000Hz)':<15} {features1['high_freq_ratio']*100:<15.1f}% {features2['high_freq_ratio']*100:<15.1f}% {high_diff*100:+.1f}%")
    
    # 音符分析
    print(f"\n🎹 音符分布分析:")
    
    # 统计音符出现频率
    brown_notes = [freq['note'] for freq in dom_freqs1]
    pink_notes = [freq['note'] for freq in dom_freqs2]
    
    from collections import Counter
    brown_note_counts = Counter(brown_notes)
    pink_note_counts = Counter(pink_notes)
    
    print(f"棕色音乐主要音符分布:")
    for note, count in brown_note_counts.most_common():
        print(f"  {note}: {count} 次")
    
    print(f"粉色音乐主要音符分布:")
    for note, count in pink_note_counts.most_common():
        print(f"  {note}: {count} 次")
    
    # 音域分析
    brown_freqs = [freq['frequency'] for freq in dom_freqs1]
    pink_freqs = [freq['frequency'] for freq in dom_freqs2]
    
    print(f"\n🎯 音域特征分析:")
    print(f"棕色音乐:")
    print(f"  最低主要频率: {min(brown_freqs):.1f} Hz ({dom_freqs1[brown_freqs.index(min(brown_freqs))]['note']})")
    print(f"  最高主要频率: {max(brown_freqs):.1f} Hz ({dom_freqs1[brown_freqs.index(max(brown_freqs))]['note']})")
    print(f"  主要频率范围: {max(brown_freqs) - min(brown_freqs):.1f} Hz")
    
    print(f"粉色音乐:")
    print(f"  最低主要频率: {min(pink_freqs):.1f} Hz ({dom_freqs2[pink_freqs.index(min(pink_freqs))]['note']})")
    print(f"  最高主要频率: {max(pink_freqs):.1f} Hz ({dom_freqs2[pink_freqs.index(max(pink_freqs))]['note']})")
    print(f"  主要频率范围: {max(pink_freqs) - min(pink_freqs):.1f} Hz")
    
    print(f"\n📈 音色特征总结:")
    
    # 明亮度对比
    if features2['spectral_centroid'] > features1['spectral_centroid']:
        print(f"1. 音色明亮度: 粉色音乐更明亮 (+{centroid_diff:.1f} Hz)")
    else:
        print(f"1. 音色明亮度: 棕色音乐更明亮 ({centroid_diff:.1f} Hz)")
    
    # 丰富度对比
    if features2['spectral_bandwidth'] > features1['spectral_bandwidth']:
        print(f"2. 音色丰富度: 粉色音乐更丰富 (+{bandwidth_diff:.1f} Hz)")
    else:
        print(f"2. 音色丰富度: 棕色音乐更丰富 ({bandwidth_diff:.1f} Hz)")
    
    # 谐波性对比
    if features1['spectral_flatness'] < features2['spectral_flatness']:
        print(f"3. 谐波特性: 棕色音乐更具谐波性 (平坦度差异: {flatness_diff:.4f})")
    else:
        print(f"3. 谐波特性: 粉色音乐更具谐波性 (平坦度差异: {flatness_diff:.4f})")
    
    # 能量分布特点
    if features1['mid_freq_ratio'] > features2['mid_freq_ratio']:
        print(f"4. 能量分布: 棕色音乐中频能量更集中 (+{mid_diff*100:.1f}%)")
    else:
        print(f"4. 能量分布: 粉色音乐中频能量更集中 (+{abs(mid_diff)*100:.1f}%)")
    
    print(f"\n📁 生成的分析图表:")
    print(f"- audio_waveform_comparison.png: 时域波形对比图")
    print(f"  显示两段音乐的时域信号特征，可观察振幅变化和时间特性")
    
    print(f"- audio_spectrum_comparison.png: 频域频谱对比图")
    print(f"  显示两段音乐的频率成分分布，可观察主要频率峰值")
    
    print(f"- audio_spectrogram_comparison.png: 频谱图和频谱重心变化图")
    print(f"  显示频率随时间的变化以及频谱重心的时间演化")
    
    print(f"\n🎼 钢琴音色特征结论:")
    print(f"1. 两段音乐都展现了典型的钢琴音色特征，具有明显的谐波结构")
    print(f"2. 粉色音乐在整体上音色更明亮、更丰富，适合表现活泼的情绪")
    print(f"3. 棕色音乐具有更强的谐波性，音色相对温暖厚重")
    print(f"4. 两段音乐的动态范围都较为适中，符合钢琴演奏的特点")
    print(f"5. 频谱分析揭示了钢琴音色的复杂性和丰富的泛音结构")
    
    print("=" * 80)

if __name__ == "__main__":
    generate_comprehensive_audio_report()
