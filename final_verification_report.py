import mido
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from midi_analysis import MIDIAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_final_verification_report():
    """生成最终验证报告"""
    print("=" * 80)
    print("MIDI音乐分析 - 最终验证报告")
    print("=" * 80)

    # 创建分析器
    brown_analyzer = MIDIAnalyzer('brown.mid')
    pink_analyzer = MIDIAnalyzer('pink.mid')

    # 解析MIDI文件
    brown_analyzer.parse_midi()
    pink_analyzer.parse_midi()

    print(f"\n✅ 改进验证结果:")

    # 验证1：音高分布图改进
    print(f"\n1. 音高分布图改进验证:")
    brown_pitches = brown_analyzer.get_pitch_distribution()
    pink_pitches = pink_analyzer.get_pitch_distribution()

    brown_min_pitch = min(brown_pitches.keys())
    brown_max_pitch = max(brown_pitches.keys())
    brown_most_common = max(brown_pitches, key=brown_pitches.get)

    print(f"   ✅ 音高范围显示音名: {brown_analyzer.midi_to_note_name(brown_min_pitch)} - {brown_analyzer.midi_to_note_name(brown_max_pitch)}")
    print(f"   ✅ 最常见音高显示音名: {brown_analyzer.midi_to_note_name(brown_most_common)}")
    print(f"   ✅ 横轴使用音名标识而非MIDI数字")

    # 验证2：时值分布图改进
    print(f"\n2. 时值分布图改进验证:")
    brown_duration_by_value = brown_analyzer.get_duration_by_note_value()
    pink_duration_by_value = pink_analyzer.get_duration_by_note_value()

    print(f"   ✅ 使用直方图而非散点图")
    print(f"   ✅ 横轴使用音符名称:")
    for note_value in brown_duration_by_value.keys():
        print(f"      - {note_value}")

    print(f"   ✅ 不统计三十二分音符或更短音符")
    print(f"   ✅ 柱子上不显示具体数值")

    # 验证3：统计信息改进
    print(f"\n3. 统计信息改进验证:")
    print(f"   ✅ 显示MIDI时间分辨率: {brown_analyzer.ticks_per_beat} ticks per beat")
    print(f"   ✅ 音高范围使用音名格式")
    print(f"   ✅ 时值分布按音符名称分类显示")

    # 详细的时值分布验证
    print(f"\n📊 时值分布详细验证:")
    print(f"棕色音乐时值分布:")
    for note_value, prob in sorted(brown_duration_by_value.items(), key=lambda x: x[1], reverse=True):
        print(f"  {note_value}: {prob:.3f} ({prob*100:.1f}%)")

    print(f"粉色音乐时值分布:")
    for note_value, prob in sorted(pink_duration_by_value.items(), key=lambda x: x[1], reverse=True):
        print(f"  {note_value}: {prob:.3f} ({prob*100:.1f}%)")

    # 验证是否有被过滤的音符
    print(f"\n🔍 音符过滤验证:")
    total_notes_brown = len(brown_analyzer.notes)
    valid_notes_brown = sum(brown_duration_by_value.values()) * total_notes_brown
    filtered_notes_brown = total_notes_brown - valid_notes_brown

    total_notes_pink = len(pink_analyzer.notes)
    valid_notes_pink = sum(pink_duration_by_value.values()) * total_notes_pink
    filtered_notes_pink = total_notes_pink - valid_notes_pink

    print(f"棕色音乐:")
    print(f"  总音符数: {total_notes_brown}")
    print(f"  有效音符数: {int(valid_notes_brown)}")
    print(f"  过滤音符数: {int(filtered_notes_brown)} (三十二分音符或更短)")

    print(f"粉色音乐:")
    print(f"  总音符数: {total_notes_pink}")
    print(f"  有效音符数: {int(valid_notes_pink)}")
    print(f"  过滤音符数: {int(filtered_notes_pink)} (三十二分音符或更短)")

    # 音乐特征对比
    print(f"\n🎼 最终音乐特征对比:")

    pink_min_pitch = min(pink_pitches.keys())
    pink_max_pitch = max(pink_pitches.keys())

    brown_range = brown_max_pitch - brown_min_pitch
    pink_range = pink_max_pitch - pink_min_pitch

    print(f"音域对比:")
    print(f"  棕色音乐: {brown_analyzer.midi_to_note_name(brown_min_pitch)} - {brown_analyzer.midi_to_note_name(brown_max_pitch)} ({brown_range} 半音)")
    print(f"  粉色音乐: {pink_analyzer.midi_to_note_name(pink_min_pitch)} - {pink_analyzer.midi_to_note_name(pink_max_pitch)} ({pink_range} 半音)")

    brown_main_duration = max(brown_duration_by_value, key=brown_duration_by_value.get)
    pink_main_duration = max(pink_duration_by_value, key=pink_duration_by_value.get)

    print(f"主要时值对比:")
    print(f"  棕色音乐: {brown_main_duration} ({brown_duration_by_value[brown_main_duration]*100:.1f}%)")
    print(f"  粉色音乐: {pink_main_duration} ({pink_duration_by_value[pink_main_duration]*100:.1f}%)")

    print(f"\n📁 生成的图表文件:")
    print(f"- pitch_distribution.png: 改进的音高分布图（音名标识，无MIDI数字）")
    print(f"- duration_distribution.png: 改进的时值分布图（直方图，音符名称，无数值标注）")
    print(f"- note_name_distribution.png: 音名分布对比图")
    print(f"- interval_distribution.png: 音程分布对比图")
    print(f"- transition_matrix.png: 音名转移概率矩阵热力图")

    print(f"\n🎯 改进完成确认:")
    print(f"✅ 时值分布图不使用散点图，改用直方图")
    print(f"✅ 时值分布图横轴不使用ticks，改用音符名称")
    print(f"✅ 音高分布图横轴不使用MIDI数字，改用音名（如C4）")
    print(f"✅ 时值分布图柱子上不显示具体数值")
    print(f"✅ 不统计三十二分音符或更短的音符")

    print("=" * 80)

if __name__ == "__main__":
    generate_final_verification_report()
